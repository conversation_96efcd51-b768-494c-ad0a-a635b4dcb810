#ifndef AMSSDK_AMS_CLIENT_H
#define AMSSDK_AMS_CLIENT_H

#include <functional>
#include <memory>
#include <string>

#include "include/common/api_result.h"

namespace amssdk {
class Workflow;
class StreamEvent;
class Knowledge;
class Chatflow;
class Chat;
class Agent;
class WorkflowRunInfoRequest;
class WorkflowRunRequest;
class WorkflowRunResponse;
class AppInfoRequest;
}  // namespace amssdk
namespace amssdk {
class AppMetaResponse;
class AppMetaRequest;
class AudioToTextRequest;
class AudioToTextResponse;

class ApiManager;

class AmsClient {
 public:
  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;

  LIBAMS_EXPORT
  explicit AmsClient(const std::string& base_url);
  LIBAMS_EXPORT ~AmsClient();

  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;

  LIBAMS_EXPORT Agent& agent();
  LIBAMS_EXPORT Chatflow& chatflow();
  LIBAMS_EXPORT Chat& chat();
  LIBAMS_EXPORT Knowledge& knowledge();
  LIBAMS_EXPORT Workflow& workflow();

 private:
  std::shared_ptr<ApiManager> api_manager_;
  std::unique_ptr<Agent> agent_api_;
  std::unique_ptr<Chatflow> chatflow_api_;
  std::unique_ptr<Chat> chat_api_;
  std::unique_ptr<Knowledge> knowledge_api_;
  std::unique_ptr<Workflow> workflow_api_;
};

}  // namespace amssdk

#endif  // AMSSDK_AMS_CLIENT_H
