#include "ams_client.h"
#include "api/api_manager.h"
#include "api/business/agent.h"
#include "api/business/chat.h"
#include "api/business/chatflow_api.h"
#include "api/business/knowledge.h"

namespace amssdk {

AmsClient::AmsClient(const std::string& base_url)
    : api_manager_(std::make_unique<ApiManager>(base_url)) {}

AmsClient::~AmsClient() = default;

bool AmsClient::SetAuthorizationKey(const std::string& key) const {
  return api_manager_->SetAuthorizationKey(key);
}

void AmsClient::SetMaxTimeout(int32_t ms) const {
  api_manager_->SetMaxTimeout(ms);
}
Agent& AmsClient::agent() {
  if (!agent_api_) {
    agent_api_ = std::make_unique<Agent>(api_manager_);
  }
  return *agent_api_;
}
Chatflow& AmsClient::chatflow() {
  if (!chatflow_api_) {
    chatflow_api_ = std::make_unique<Chatflow>(api_manager_);
  }
  return *chatflow_api_;
}
Chat& AmsClient::chat() {
  if (!chat_api_) {
    chat_api_ = std::make_unique<Chat>(api_manager_);
  }
  return *chat_api_;
}

Knowledge& AmsClient::knowledge() {
  if (!knowledge_api_) {
    knowledge_api_ = std::make_unique<Knowledge>(api_manager_);
  }
  return *knowledge_api_;
}
Workflow& AmsClient::workflow() {
  if (!workflow_api_) {
    workflow_api_ = std::make_unique<Workflow>(api_manager_);
  }
  return *workflow_api_;
}

}  // namespace amssdk
