#ifndef AMSSDK_API_MANAGER_H
#define AMSSDK_API_MANAGER_H

#include <memory>
#include <string>
#include "network/http_client.h"

namespace amssdk {
class WorkflowService;
class AudioService;
class ConversationService;
class ChatService;
class TaskService;
class FileService;
class AppService;
class KnowledgeService;

class ApiManager {
 public:
  LIBAMS_EXPORT
  explicit ApiManager(const std::string& base_url);

  LIBAMS_EXPORT ~ApiManager();

  LIBAMS_EXPORT ChatService& chat();

  LIBAMS_EXPORT TaskService& task() const;

  LIBAMS_EXPORT FileService& file() const;

  LIBAMS_EXPORT AudioService& audio() const;

  LIBAMS_EXPORT ConversationService& conversation() const;

  LIBAMS_EXPORT AppService& app() const;

  LIBAMS_EXPORT WorkflowService& workflow() const;

  LIBAMS_EXPORT KnowledgeService& knowledge() const;

  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key);

  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms);

  LIBAMS_EXPORT void SetBaseUrl(const std::string& base_url);

  HttpClient& GetHttpClient() { return http_client_; }
  Authorization& GetAuthorization() { return auth_; }

 private:
  std::string base_url_;
  HttpClient http_client_;
  Authorization& auth_;

  std::unique_ptr<ChatService> chat_service_;
  std::unique_ptr<TaskService> task_service_;
  std::unique_ptr<FileService> file_service_;
  std::unique_ptr<ConversationService> conversation_service_;
  std::unique_ptr<AudioService> audio_service_;
  std::unique_ptr<AppService> app_service_;
  std::unique_ptr<WorkflowService> workflow_service_;
  std::unique_ptr<KnowledgeService> knowledge_service_;
  void InitializeServices();
};

}  // namespace amssdk

#endif  // AMSSDK_API_MANAGER_H
