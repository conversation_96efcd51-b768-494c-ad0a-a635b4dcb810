#include "knowledge.h"

#include "api/api_manager.h"
#include "api/services/knowledge_service.h"
#include "include/knowledge/knowledge.h"
#include "include/knowledge/knowledge_segments.h"

namespace amssdk {

Knowledge::Knowledge(std::shared_ptr<ApiManager> api_manager)
    : api_manager_(api_manager) {}

// 知识库管理实现
ApiResult<CreateDatasetResponse> Knowledge::CreateDataset(
    const CreateDatasetRequest& request) const {
  return api_manager_->knowledge().CreateDataset(request);
}

ApiResult<ListDatasetsResponse> Knowledge::ListDatasets(
    const ListDatasetsRequest& request) const {
  return api_manager_->knowledge().ListDatasets(request);
}

ApiResult<SimpleResponse> Knowledge::DeleteDataset(
    const DeleteDatasetRequest& request) const {
  return api_manager_->knowledge().DeleteDataset(request);
}

ApiResult<RetrieveDatasetResponse> Knowledge::RetrieveDataset(
    const RetrieveDatasetRequest& request) const {
  return api_manager_->knowledge().RetrieveDataset(request);
}

// 文档管理实现
ApiResult<CreateDocumentResponse> Knowledge::CreateDocumentByText(
    const CreateDocumentByTextRequest& request) const {
  return api_manager_->knowledge().CreateDocumentByText(request);
}

ApiResult<CreateDocumentResponse> Knowledge::CreateDocumentByFile(
    const CreateDocumentByFileRequest& request) const {
  return api_manager_->knowledge().CreateDocumentByFile(request);
}

ApiResult<CreateDocumentResponse> Knowledge::UpdateDocumentByText(
    const UpdateDocumentByTextRequest& request) const {
  return api_manager_->knowledge().UpdateDocumentByText(request);
}

ApiResult<CreateDocumentResponse> Knowledge::UpdateDocumentByFile(
    const UpdateDocumentByFileRequest& request) const {
  return api_manager_->knowledge().UpdateDocumentByFile(request);
}

ApiResult<GetIndexingStatusResponse> Knowledge::GetIndexingStatus(
    const GetIndexingStatusRequest& request) const {
  return api_manager_->knowledge().GetIndexingStatus(request);
}

ApiResult<DeleteDocumentResponse> Knowledge::DeleteDocument(
    const DeleteDocumentRequest& request) const {
  return api_manager_->knowledge().DeleteDocument(request);
}

ApiResult<ListDocumentsResponse> Knowledge::ListDocuments(
    const ListDocumentsRequest& request) const {
  return api_manager_->knowledge().ListDocuments(request);
}

// 分段管理实现
ApiResult<CreateSegmentResponse> Knowledge::CreateSegment(
    const CreateSegmentRequest& request) const {
  return api_manager_->knowledge().CreateSegment(request);
}

ApiResult<ListSegmentsResponse> Knowledge::ListSegments(
    const ListSegmentsRequest& request) const {
  return api_manager_->knowledge().ListSegments(request);
}

ApiResult<DeleteSegmentResponse> Knowledge::DeleteSegment(
    const DeleteSegmentRequest& request) const {
  return api_manager_->knowledge().DeleteSegment(request);
}

ApiResult<UpdateSegmentResponse> Knowledge::UpdateSegment(
    const UpdateSegmentRequest& request) const {
  return api_manager_->knowledge().UpdateSegment(request);
}

}  // namespace amssdk
