#ifndef AMSSDK_KNOWLEDGE_H
#define AMSSDK_KNOWLEDGE_H

#include <memory>
#include <string>
#include "include/common/api_result.h"

namespace amssdk {

class ApiManager;

// 前向声明所有请求和响应类
class CreateDocumentByTextRequest;
class CreateDocumentResponse;
class CreateDocumentByFileRequest;
class UpdateDocumentByTextRequest;
class UpdateDocumentByFileRequest;
class GetIndexingStatusRequest;
class GetIndexingStatusResponse;
class DeleteDocumentRequest;
class DeleteDocumentResponse;
class ListDocumentsRequest;
class ListDocumentsResponse;
class CreateDatasetRequest;
class CreateDatasetResponse;
class ListDatasetsRequest;
class ListDatasetsResponse;
class DeleteDatasetRequest;
class CreateSegmentRequest;
class CreateSegmentResponse;
class ListSegmentsRequest;
class ListSegmentsResponse;
class DeleteSegmentRequest;
class DeleteSegmentResponse;
class UpdateSegmentRequest;
class UpdateSegmentResponse;
class RetrieveDatasetRequest;
class RetrieveDatasetResponse;
class SimpleResponse;

class Knowledge {
 public:
  explicit Knowledge(std::shared_ptr<ApiManager> api_manager);

  // 知识库管理
  LIBAMS_EXPORT ApiResult<CreateDatasetResponse> CreateDataset(
      const CreateDatasetRequest& request) const;
  LIBAMS_EXPORT ApiResult<ListDatasetsResponse> ListDatasets(
      const ListDatasetsRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> DeleteDataset(
      const DeleteDatasetRequest& request) const;
  LIBAMS_EXPORT ApiResult<RetrieveDatasetResponse> RetrieveDataset(
      const RetrieveDatasetRequest& request) const;

  // 文档管理
  LIBAMS_EXPORT ApiResult<CreateDocumentResponse> CreateDocumentByText(
      const CreateDocumentByTextRequest& request) const;
  LIBAMS_EXPORT ApiResult<CreateDocumentResponse> CreateDocumentByFile(
      const CreateDocumentByFileRequest& request) const;
  LIBAMS_EXPORT ApiResult<CreateDocumentResponse> UpdateDocumentByText(
      const UpdateDocumentByTextRequest& request) const;
  LIBAMS_EXPORT ApiResult<CreateDocumentResponse> UpdateDocumentByFile(
      const UpdateDocumentByFileRequest& request) const;
  LIBAMS_EXPORT ApiResult<GetIndexingStatusResponse> GetIndexingStatus(
      const GetIndexingStatusRequest& request) const;
  LIBAMS_EXPORT ApiResult<DeleteDocumentResponse> DeleteDocument(
      const DeleteDocumentRequest& request) const;
  LIBAMS_EXPORT ApiResult<ListDocumentsResponse> ListDocuments(
      const ListDocumentsRequest& request) const;

  // 分段管理
  LIBAMS_EXPORT ApiResult<CreateSegmentResponse> CreateSegment(
      const CreateSegmentRequest& request) const;
  LIBAMS_EXPORT ApiResult<ListSegmentsResponse> ListSegments(
      const ListSegmentsRequest& request) const;
  LIBAMS_EXPORT ApiResult<DeleteSegmentResponse> DeleteSegment(
      const DeleteSegmentRequest& request) const;
  LIBAMS_EXPORT ApiResult<UpdateSegmentResponse> UpdateSegment(
      const UpdateSegmentRequest& request) const;

 private:
  std::shared_ptr<ApiManager> api_manager_;
};

}  // namespace amssdk

#endif  //AMSSDK_KNOWLEDGE_H
